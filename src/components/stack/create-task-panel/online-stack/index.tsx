import { But<PERSON> } from "@/components/ui/button";
import { Arrow<PERSON><PERSON><PERSON>, Loader2, Trash2, VideoIcon, Edit3 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useEffect, useMemo, useState } from "react";
import { OnlineStackProps, VideoUrlFormData, videoUrlSchema } from "./types";
import { useVideoInfo } from "@/hooks/swr/use-video-info";
import FormGenerator from "@/components/common/form-generator";
import { FormatButton } from "./components/format-selection/format-button";
import {
  currentTaskAtom,
  resetCurrentTaskAtom,
  showPreviewSubtitleAtom,
  showVideoPreviewPanelAtom,
} from "@/stores/slices/current_task";
import { subtitleDataAtom } from "@/stores/slices/subtitle_store";
import { useAtom, useSetAtom, useAtomValue } from "jotai";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { getBestFormats } from "@/utils/video-format";
import { TaskSettings } from "./components/task-settings";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "sonner";
import { createScopedLogger } from "@/utils";
import { env } from "@/env";

const logger = createScopedLogger("online-stack");

// Helper function to generate SRT content from subtitle data
const generateSrtContent = (subtitleData: any) => {
  const { originalSubtitles, translatedSubtitles } = subtitleData;

  return originalSubtitles
    .map((sub: any, index: number) => {
      const translatedSub = translatedSubtitles[index];

      // Format: index, time range, original text, translated text (if available)
      let content = `${index + 1}\n${sub.startTime} --> ${sub.endTime}\n${sub.text}`;

      if (translatedSub && translatedSub.translatedText) {
        content += `\n${translatedSub.translatedText}`;
      }

      return content;
    })
    .join('\n\n');
};

// Helper function to upload SRT file and get URL
const uploadSrtFile = async (srtContent: string): Promise<string> => {
  const apiHost = env.NEXT_PUBLIC_BASE_API_HOST || 'http://127.0.0.1:9011';

  // Create a blob from the SRT content
  const blob = new Blob([srtContent], { type: 'text/plain' });
  const file = new File([blob], 'subtitles.srt', { type: 'text/plain' });

  // Create form data for upload
  const formData = new FormData();
  formData.append('file', file);

  // Upload the file
  const response = await fetch(`${apiHost}/upload`, {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    throw new Error(`Upload failed: ${response.statusText}`);
  }

  const result = await response.json();

  if (result.code !== 0) {
    throw new Error(`Upload failed: ${result.msg || 'Unknown error'}`);
  }

  return result.data.url;
};

// Helper function to call dubbing API and return task_id
const submitDubbingTask = async (payload: any): Promise<string> => {
  const apiHost = env.NEXT_PUBLIC_BASE_API_HOST || 'http://127.0.0.1:9011';

  const response = await fetch(`${apiHost}/dubbing`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(payload),
  });

  if (!response.ok) {
    throw new Error(`Dubbing API failed: ${response.status} ${response.statusText}`);
  }

  const result = await response.json();

  if (!result.task_id) {
    throw new Error('No task_id returned from dubbing API');
  }

  return result.task_id;
};

// Helper function to poll task status until completion with result URLs
const pollTaskStatus = async (taskId: string, onStatusUpdate: (status: string) => void): Promise<string[]> => {
  const apiHost = env.NEXT_PUBLIC_BASE_API_HOST || 'http://127.0.0.1:9011';
  const maxAttempts = 120; // 10 minutes with 5-second intervals
  const pollInterval = 5000; // 5 seconds
  let attempts = 0;

  const poll = async (): Promise<string[]> => {
    try {
      attempts++;
      const statusEndpoint = `${apiHost}/task_status?task_id=${taskId}`;

      logger.info(`[POLL ${attempts}] Checking dubbing task status:`, statusEndpoint);
      console.log(`🔄 [POLL ${attempts}] Checking task status for: ${taskId}`);

      const response = await fetch(statusEndpoint, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Status check failed: ${response.status} ${response.statusText}`);
      }

      const statusData = await response.json();
      logger.info(`[POLL ${attempts}] Task status response:`, statusData);
      console.log(`📊 [POLL ${attempts}] Status data:`, statusData);

      // Check if task is complete with success response format
      if (statusData.code === 0 && statusData.data && statusData.data.url && Array.isArray(statusData.data.url)) {
        logger.info('Dubbing task completed successfully with result URLs', {
          taskId,
          urls: statusData.data.url,
          statusData
        });
        onStatusUpdate('Task completed successfully!');
        return statusData.data.url; // Return the array of URLs
      }

      // Handle status messages for user feedback
      let displayStatus = '';
      if (statusData.msg && statusData.msg !== 'ok') {
        displayStatus = statusData.msg; // Use the message like "No.1 on prepare queue"
      } else if (statusData.status) {
        displayStatus = statusData.status;
      } else if (statusData.code === -1) {
        displayStatus = 'Processing...';
      } else if (statusData.code !== undefined && statusData.code !== 0) {
        displayStatus = `Processing... (code: ${statusData.code})`;
      } else {
        displayStatus = 'Processing...';
      }

      onStatusUpdate(displayStatus);

      // Check for explicit failure (only specific failure statuses, not code-based)
      if (statusData.status === 'failed' || statusData.status === 'error') {
        const errorMsg = statusData.error || statusData.message || statusData.msg || 'Task failed';
        logger.error('Dubbing task failed', { taskId, error: errorMsg, statusData });
        throw new Error(`Task failed: ${errorMsg}`);
      }

      // Continue polling if not complete and not exceeded max attempts
      if (attempts < maxAttempts) {
        console.log(`⏳ [POLL ${attempts}] Waiting ${pollInterval/1000}s before next poll...`);
        await new Promise(resolve => setTimeout(resolve, pollInterval));
        console.log(`🔄 [POLL ${attempts}] Continuing to poll...`);
        return await poll(); // Recursive call to continue polling
      } else {
        console.log(`❌ [POLL ${attempts}] Polling timeout - maximum attempts exceeded`);
        throw new Error('Task polling timeout - maximum attempts exceeded');
      }
    } catch (error) {
      logger.error('Error during dubbing task polling', {
        taskId,
        attempt: attempts,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  };

  // Start polling and return the result URLs
  return await poll();
};

export const OnlineStack = ({ stackRef }: OnlineStackProps) => {
  const t = useTranslations();
  const [currentTask, setCurrentTask] = useAtom(currentTaskAtom);
  const resetCurrentTask = useSetAtom(resetCurrentTaskAtom);
  const [showPreviewSubtitle, setShowPreviewSubtitle] = useAtom(
    showPreviewSubtitleAtom
  );
  const [, setShowVideoPreviewPanel] = useAtom(showVideoPreviewPanelAtom);
  const subtitleData = useAtomValue(subtitleDataAtom);
  const [isSubmittingTask, setIsSubmittingTask] = useState(false);
  const [taskStatus, setTaskStatus] = useState<string>("");

  // Prevent page navigation during task processing
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (isSubmittingTask) {
        e.preventDefault();
        // Modern browsers will show a generic message
        return '';
      }
    };

    if (isSubmittingTask) {
      window.addEventListener('beforeunload', handleBeforeUnload);
    }

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [isSubmittingTask]);

  const {
    watch,
    register,
    handleSubmit,
    setValue: setValueForm,
    formState: { errors, isSubmitting },
  } = useForm<VideoUrlFormData>({
    defaultValues: { videoUrl: currentTask.videoUrl },
    resolver: zodResolver(videoUrlSchema, {
      errorMap: (issue) => ({
        message:
          issue.code === "too_small"
            ? t("form.errors.required")
            : t("form.errors.invalidVideoUrl"),
      }),
    }),
  });

  const {
    data: videoInfo,
    error,
    trigger: getVideoInfo,
    isMutating: isLoading,
  } = useVideoInfo();

  // Check if current task is an uploaded video
  const isUploadedVideo = !!(
    currentTask.name &&
    currentTask.duration &&
    currentTask.thumbnail
  );

  // Get the best quality formats from available formats
  const bestFormats = useMemo(() => {
    return videoInfo?.info?.formats
      ? getBestFormats(videoInfo.info.formats)
      : [];
  }, [videoInfo]);

  // Auto-select the first format when video info is loaded
  useEffect(() => {
    if (
      videoInfo?.info &&
      bestFormats.length > 0 &&
      !currentTask.settings.selectedFormat
    ) {
      setCurrentTask((prev) => ({
        ...prev,
        settings: {
          ...prev.settings,
          selectedFormat: bestFormats[0],
        },
      }));
      toast.success(t("global.video.formatSelected"));
    }
  }, [
    videoInfo,
    bestFormats,
    currentTask.settings.selectedFormat,
    setCurrentTask,
    t,
  ]);

  // Show/hide video preview panel based on video availability
  useEffect(() => {
    const hasVideoData = !!(videoInfo?.info || isUploadedVideo);
    setShowVideoPreviewPanel(hasVideoData);

    // Hide video preview panel when component unmounts
    return () => {
      setShowVideoPreviewPanel(false);
    };
  }, [videoInfo?.info, isUploadedVideo, setShowVideoPreviewPanel]);

  // Handle video URL form submission
  const handleFormSubmit = useCallback(
    (data: VideoUrlFormData) => {
      setCurrentTask((prev) => ({ ...prev, videoUrl: data.videoUrl }));
      getVideoInfo({ videoUrl: data.videoUrl });
      toast.info(t("global.video.gettingInfo"));
    },
    [setCurrentTask, getVideoInfo, t]
  );

  // Handle task creation confirmation
  const handleConfirm = useCallback(async () => {
    console.log("🚀 Starting task submission...");

    // Set submitting state
    setIsSubmittingTask(true);
    setTaskStatus("Preparing task...");
    console.log("✅ Set isSubmittingTask to true");

    try {
      let subtitleUrl = null;
      let videoUrl = currentTask.videoUrl;

      // Upload subtitles if they exist
      if (subtitleData.originalSubtitles.length > 0) {
        setTaskStatus("Uploading subtitles...");
        logger.info("Uploading subtitles before sending task", {
          taskId: currentTask.id,
          subtitleCount: subtitleData.originalSubtitles.length,
        });

        // Generate SRT content from subtitles
        const srtContent = generateSrtContent(subtitleData);

        // Upload SRT file and get URL
        subtitleUrl = await uploadSrtFile(srtContent);

        logger.info("Subtitles uploaded successfully", {
          taskId: currentTask.id,
          subtitleUrl,
        });
      } else {
        throw new Error("No subtitles available. Please create subtitles first using the subtitle editor.");
      }

      // Ensure we have a video URL
      if (!videoUrl) {
        throw new Error("No video URL available");
      }

      // Prepare dubbing API payload
      const dubbingPayload = {
        srt_url: subtitleUrl,
        video_url: videoUrl,
        voiceSeparation: currentTask.settings.voiceSeparation,
        sourceLanguage: currentTask.settings.sourceLanguage,
        targetLanguage: currentTask.settings.targetLanguage,
        subtitleLayout: currentTask.settings.subtitleLayout,
        subtitleStyle: {
          fontSize: currentTask.settings.subtitleStyle.fontSize,
          fontFamily: currentTask.settings.subtitleStyle.fontFamily,
          primaryColor: currentTask.settings.subtitleStyle.primaryColor,
          primaryStrokeWidth: currentTask.settings.subtitleStyle.primaryStrokeWidth,
          showPrimaryStroke: currentTask.settings.subtitleStyle.showPrimaryStroke,
          primaryMarginV: currentTask.settings.subtitleStyle.primaryMarginV,
        },
      };

      setTaskStatus("Submitting task...");
      logger.info("Sending task data to /dubbing endpoint", {
        taskId: currentTask.id,
        payload: dubbingPayload,
      });

      // Submit dubbing task and get task_id
      const taskId = await submitDubbingTask(dubbingPayload);

      logger.info("Dubbing task submitted successfully", {
        originalTaskId: currentTask.id,
        dubbingTaskId: taskId,
      });

      setTaskStatus("Task submitted! Waiting for processing...");
      toast.success("Task submitted successfully! Processing...");

      // Start polling task status with status updates and wait for result URLs
      const resultUrls = await pollTaskStatus(taskId, (status) => {
        logger.info("Dubbing task status update", { taskId, status });
        setTaskStatus(status);

        // Show status to user
        if (status.includes('queue') || status.includes('prepare')) {
          // Show queue position updates
          if (status.includes('No.')) {
            toast.info(status); // Show queue position like "No.1 on prepare queue"
          }
        } else if (status === 'processing' || status === 'Processing...') {
          // Only show processing toast once to avoid spam
          if (!status.includes('code:')) {
            toast.info("Task is being processed...");
          }
        } else if (status === 'Task completed successfully!') {
          toast.success("Task completed successfully!");
        } else {
          // Show other status updates
          toast.info(`Task status: ${status}`);
        }
      });

      // Task completed successfully with result URLs
      logger.info("Dubbing task completed with result URLs", {
        taskId,
        resultUrls,
      });

      // Show result URLs to user
      if (resultUrls && resultUrls.length > 0) {
        toast.success(`Video dubbing completed! ${resultUrls.length} output file(s) generated.`);

        // Log the result URLs for user reference
        resultUrls.forEach((url, index) => {
          logger.info(`Result video ${index + 1}: ${url}`);
          console.log(`✅ Output video ${index + 1}: ${url}`);
        });

        // Show download links to user
        resultUrls.forEach((url, index) => {
          toast.success(`✅ Output video ${index + 1} ready!`, {
            duration: 8000,
            action: {
              label: "Download",
              onClick: () => {
                // Open download link in new tab
                window.open(url, '_blank');
              },
            },
          });
        });
      }

      // Now we can close and reset
      setShowVideoPreviewPanel(false);
      stackRef.current?.pop();
      resetCurrentTask();

      toast.success("Video dubbing process completed successfully!");

    } catch (error) {
      logger.error("Failed to submit dubbing task", {
        taskId: currentTask.id,
        error: error instanceof Error ? error.message : String(error),
      });

      toast.error(`Failed to submit task: ${error instanceof Error ? error.message : String(error)}`);
      // Don't reset task if there was an error - let user try again
    } finally {
      console.log("🔄 Resetting task submission state...");
      setIsSubmittingTask(false);
      setTaskStatus("");
      console.log("✅ Reset isSubmittingTask to false");
    }
  }, [
    currentTask,
    videoInfo,
    stackRef,
    resetCurrentTask,
    setShowVideoPreviewPanel,
    subtitleData,
    t,
  ]);

  // Handle task removal
  const handleRemove = useCallback(() => {
    setShowVideoPreviewPanel(false);
    resetCurrentTask();
    stackRef.current?.pop();
    toast.success(t("global.video.taskRemoved"));
  }, [resetCurrentTask, stackRef, setShowVideoPreviewPanel, t]);

  return (
    <div className="flex size-full flex-col">
      <div className="flex items-center gap-2 border-b px-3 py-2">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => {
            if (isSubmittingTask) {
              toast.warning("Cannot navigate back while task is processing");
              return;
            }
            setShowVideoPreviewPanel(false);
            stackRef.current?.pop();
          }}
          disabled={isSubmittingTask}
          className="size-6"
        >
          <ArrowLeft className="size-3.5" />
        </Button>
        <span className="text-xs font-medium">
          {isUploadedVideo
            ? t("global.video.uploadedVideo")
            : t("global.video.onlineVideo")}
          {isSubmittingTask && (
            <span className="ml-2 text-orange-600">• Processing</span>
          )}
        </span>
        <div className="flex-1" />
        <Button
          variant="ghost"
          size="icon"
          onClick={() => {
            if (isSubmittingTask) {
              toast.warning("Cannot remove task while processing");
              return;
            }
            handleRemove();
          }}
          disabled={isSubmittingTask}
          className="size-6 text-destructive hover:text-destructive disabled:text-muted-foreground"
        >
          <Trash2 className="size-3.5" />
        </Button>
      </div>

      <ScrollArea className="flex-1">
        <div className="flex flex-col gap-3 p-3">
          {!isUploadedVideo && (
            <form
              onSubmit={handleSubmit(handleFormSubmit)}
              className="space-y-2"
            >
              <div className="flex flex-col items-end gap-2">
                <div className="w-full flex-1">
                  <FormGenerator
                    id="videoUrl"
                    inputType="input"
                    name="videoUrl"
                    errors={errors}
                    register={register}
                    setValue={setValueForm}
                    watch={watch}
                    label={t("form.fields.videoUrl.label")}
                    placeholder={t("form.fields.videoUrl.placeholder")}
                  />
                </div>
                <Button
                  type="submit"
                  disabled={isSubmitting || isLoading}
                  className="max-w-fit"
                  size="sm"
                >
                  {isSubmitting || isLoading ? (
                    <Loader2 className="mr-1.5 size-3.5 animate-spin" />
                  ) : (
                    <VideoIcon className="mr-1.5 size-3.5" />
                  )}
                  {t("form.get_video_info")}
                </Button>
              </div>

              {error?.message && (
                <p className="text-xs text-destructive">{error.message}</p>
              )}
            </form>
          )}

          {/* Subtitle Editor Option */}
          <div className="space-y-2">
            <div className="flex items-center justify-between px-1">
              <span className="text-xs font-medium text-muted-foreground">
                Subtitle Editor
              </span>
            </div>
            <Button
              variant="outline"
              className="w-full"
              size="sm"
              disabled={isSubmittingTask}
              onClick={() => {
                if (isSubmittingTask) {
                  toast.warning("Cannot open subtitle editor while task is processing");
                  return;
                }
                stackRef.current?.push("subtitle-editor-with-video");
              }}
            >
              <Edit3 className="mr-1.5 size-3.5" />
              Open Subtitle Editor
            </Button>
          </div>

          {(videoInfo?.info || isUploadedVideo) && (
            <div className="space-y-3">
              <div className="space-y-3">
                {!isUploadedVideo && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between px-1">
                      <span className="text-xs font-medium text-muted-foreground">
                        {t("form.fields.formats")}
                      </span>
                      <span className="text-[0.7rem] text-muted-foreground">
                        {bestFormats.length} {t("form.fields.formatsAvailable")}
                      </span>
                    </div>
                    <div className="grid gap-1.5 @sm:grid-cols-2">
                      {bestFormats.map((format) => (
                        <FormatButton
                          key={format.formatId}
                          format={format}
                          isSelected={
                            currentTask.settings.selectedFormat?.formatId ===
                            format.formatId
                          }
                          onSelect={() => {
                            if (isSubmittingTask) {
                              toast.warning("Cannot change format while task is processing");
                              return;
                            }
                            setCurrentTask((prev) => ({
                              ...prev,
                              settings: {
                                ...prev.settings,
                                selectedFormat: format,
                              },
                            }));
                            toast.success(t("global.video.formatSelected"));
                          }}
                        />
                      ))}
                    </div>
                  </div>
                )}

                <div className="rounded-lg border bg-card p-3">
                  <TaskSettings
                    voiceSeparation={currentTask.settings.voiceSeparation}
                    sourceLanguage={currentTask.settings.sourceLanguage}
                    targetLanguage={currentTask.settings.targetLanguage}
                    subtitleLayout={currentTask.settings.subtitleLayout}
                    subtitleStyle={currentTask.settings.subtitleStyle}
                    showSubtitle={showPreviewSubtitle}
                    voiceDubbingEnabled={currentTask.settings.voiceDubbing?.enabled ?? false}
                    voiceDubbingService={currentTask.settings.voiceDubbing?.service ?? 0}
                    voiceDubbingVoiceName={currentTask.settings.voiceDubbing?.voiceName ?? ""}
                    onVoiceSeparationChange={(checked) => {
                      setCurrentTask((prev) => ({
                        ...prev,
                        settings: {
                          ...prev.settings,
                          voiceSeparation: checked,
                        },
                      }));
                    }}
                    onSourceLanguageChange={(value) => {
                      setCurrentTask((prev) => ({
                        ...prev,
                        settings: {
                          ...prev.settings,
                          sourceLanguage: value,
                        },
                      }));
                    }}
                    onTargetLanguageChange={(value) => {
                      setCurrentTask((prev) => ({
                        ...prev,
                        settings: {
                          ...prev.settings,
                          targetLanguage: value,
                        },
                      }));
                    }}
                    onSubtitleLayoutChange={(value) => {
                      setCurrentTask((prev) => ({
                        ...prev,
                        settings: {
                          ...prev.settings,
                          subtitleLayout: value,
                        },
                      }));
                    }}
                    onSubtitleStyleChange={(updates) => {
                      setCurrentTask((prev) => ({
                        ...prev,
                        settings: {
                          ...prev.settings,
                          subtitleStyle: {
                            ...prev.settings.subtitleStyle,
                            ...updates,
                          },
                        },
                      }));
                    }}
                    onShowSubtitleChange={(show) =>
                      setShowPreviewSubtitle(show)
                    }
                    onVoiceDubbingEnabledChange={(enabled) => {
                      setCurrentTask((prev) => {
                        const defaultVoiceDubbing = {
                          enabled: false,
                          service: 0,
                          voiceName: "",
                        };
                        return {
                          ...prev,
                          settings: {
                            ...prev.settings,
                            voiceDubbing: {
                              ...defaultVoiceDubbing,
                              ...(prev.settings.voiceDubbing || {}),
                              enabled,
                            },
                          },
                        };
                      });
                    }}
                    onVoiceDubbingServiceChange={(service) => {
                      setCurrentTask((prev) => {
                        const defaultVoiceDubbing = {
                          enabled: false,
                          service: 0,
                          voiceName: "",
                        };
                        return {
                          ...prev,
                          settings: {
                            ...prev.settings,
                            voiceDubbing: {
                              ...defaultVoiceDubbing,
                              ...(prev.settings.voiceDubbing || {}),
                              service,
                              voiceName: "", // Reset voice when service changes
                            },
                          },
                        };
                      });
                    }}
                    onVoiceDubbingVoiceNameChange={(voiceName) => {
                      setCurrentTask((prev) => {
                        const defaultVoiceDubbing = {
                          enabled: false,
                          service: 0,
                          voiceName: "",
                        };
                        return {
                          ...prev,
                          settings: {
                            ...prev.settings,
                            voiceDubbing: {
                              ...defaultVoiceDubbing,
                              ...(prev.settings.voiceDubbing || {}),
                              voiceName,
                            },
                          },
                        };
                      });
                    }}
                  />
                </div>
              </div>

              {/* Task Status Display */}
              {(taskStatus || isSubmittingTask) && (
                <div className="space-y-3">
                  <div className="rounded-lg border bg-card p-4">
                    <div className="flex items-center justify-center gap-3">
                      <Loader2 className="size-5 animate-spin text-primary" />
                      <div className="text-center">
                        <div className="text-sm font-medium text-foreground">
                          {taskStatus || "Preparing task..."}
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          Please wait, do not close this page
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Progress indicator */}
                  <div className="w-full bg-secondary rounded-full h-2">
                    <div className="bg-primary h-2 rounded-full animate-pulse" style={{
                      width: taskStatus?.includes('completed') ? '100%' :
                             taskStatus?.includes('processing') ? '70%' :
                             taskStatus?.includes('queue') || taskStatus?.includes('prepare') ? '30%' : '10%'
                    }}></div>
                  </div>
                </div>
              )}

              <Button
                className="w-full"
                onClick={handleConfirm}
                disabled={
                  isSubmittingTask ||
                  (!isUploadedVideo && !currentTask.settings.selectedFormat)
                }
                size="sm"
                variant={isSubmittingTask ? "secondary" : "default"}
              >
                {isSubmittingTask ? (
                  <>
                    <Loader2 className="mr-2 size-4 animate-spin" />
                    {taskStatus?.includes('completed') ? 'Finishing...' :
                     taskStatus?.includes('processing') ? 'Processing...' :
                     taskStatus?.includes('queue') || taskStatus?.includes('prepare') ? 'In Queue...' :
                     taskStatus?.includes('Submitting') ? 'Submitting...' :
                     taskStatus?.includes('Uploading') ? 'Uploading...' :
                     'Processing...'}
                  </>
                ) : (
                  <>
                    <VideoIcon className="mr-2 size-4" />
                    {t("global.video.confirmSelection")}
                  </>
                )}
              </Button>

              {/* Additional info when processing */}
              {isSubmittingTask && (
                <div className="text-center text-xs text-muted-foreground">
                  <p>⚠️ Task is being processed. Please keep this page open.</p>
                  <p>You will be notified when the video is ready for download.</p>
                </div>
              )}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
};
